#!/bin/bash

# 部署参数配置
WORK_DIR="/www/wwwroot/jishou"
CRON_JOB="*/10 * * * * /bin/bash ${WORK_DIR}/monitor.sh >> ${WORK_DIR}/cron.log 2>&1"
CRON_LOG="${WORK_DIR}/cron.log"
MONITOR_SCRIPT="${WORK_DIR}/monitor.sh"

# 创建日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$CRON_LOG"
}

# 检查并安装 cron 服务
install_cron() {
    if ! command -v crontab &> /dev/null; then
        log "检测到未安装 cron，正在自动安装..."
        if command -v yum &> /dev/null; then
            yum install cronie -y && systemctl enable --now crond
        elif command -v apt-get &> /dev/null; then
            apt-get update && apt-get install cron -y && systemctl enable --now cron
        else
            log "错误：无法识别的系统，请手动安装 cron 服务"
            exit 1
        fi
        log "cron 服务安装完成"
    fi
}

# 检查脚本权限
check_script() {
    if [ ! -f "$MONITOR_SCRIPT" ]; then
        log "错误：监控脚本不存在于 $MONITOR_SCRIPT"
        exit 1
    fi

    if [ ! -x "$MONITOR_SCRIPT" ]; then
        log "设置执行权限：$MONITOR_SCRIPT"
        chmod +x "$MONITOR_SCRIPT"
    fi
}

# 部署定时任务
deploy_cron() {
    log "正在部署定时任务..."
    
    # 检查是否已存在相同任务
    if crontab -l | grep -Fq "$MONITOR_SCRIPT"; then
        log "检测到已存在的定时任务，先移除旧任务"
        crontab -l | grep -vF "$MONITOR_SCRIPT" | crontab -
    fi

    # 添加新任务
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    
    # 验证部署
    if crontab -l | grep -Fq "$MONITOR_SCRIPT"; then
        log "定时任务部署成功！当前 cron 任务列表："
        crontab -l | tee -a "$CRON_LOG"
    else
        log "错误：定时任务部署失败"
        exit 1
    fi
}

# 主流程
main() {
    log "====== 开始部署定时监控任务 ======"
    install_cron
    check_script
    deploy_cron
    log "====== 部署完成 ======\n"
}

main