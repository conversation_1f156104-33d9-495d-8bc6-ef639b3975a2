import json
import asyncio
import logging
import datetime
from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.error import TelegramError
from telegram.constants import ParseMode
from telegram.request import HTTPXRequest

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        
        logging.FileHandler(f'broadcast_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置
BOT_TOKEN = "8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI"  # 替换为你的bot token
DB_FILE = "users.json"
BROADCAST_CONTENT = """
   体验次数不足怎么办?
💡 签到得查询次数 | 邀请3人享1天无限查！
"""

async def load_users():
    """加载用户数据"""
    try:
        with open(DB_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
            users = [int(user_id) for user_id in data.get("users", {}).keys()]
            logger.info(f"成功加载 {len(users)} 个用户")
            return users
    except Exception as e:
        logger.error(f"加载用户数据失败: {str(e)}")
        return []

async def send_message(bot, user_id, content):
    """发送消息给单个用户"""
    try:
        # 创建内联键盘
        keyboard = [
            [
                InlineKeyboardButton("每日签到", callback_data="sign_in"),
                InlineKeyboardButton("邀请好友", callback_data="invite")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await bot.send_message(
            chat_id=user_id,
            text=content,
            parse_mode='HTML',
            reply_markup=reply_markup
        )
        logger.info(f"成功发送消息给用户 {user_id}")
        return True
    except TelegramError as e:
        logger.error(f"发送消息给用户 {user_id} 失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"发送消息给用户 {user_id} 时发生未知错误: {str(e)}")
        return False

async def broadcast_messages():
    """广播消息给所有用户"""
    logger.info("开始广播任务")
    logger.info(f"广播内容: {BROADCAST_CONTENT}")
    
    # 加载用户
    users = await load_users()
    if not users:
        logger.error("没有找到用户，广播终止")
        return
    
    # 创建bot实例，配置连接池
    request = HTTPXRequest(
        connection_pool_size=5,    # 减小连接池大小
        pool_timeout=60.0,         # 增加连接池超时时间
        read_timeout=60.0,         # 增加读取超时时间
        write_timeout=60.0         # 增加写入超时时间
    )
    bot = Bot(token=BOT_TOKEN, request=request)
    
    # 统计变量
    total_users = len(users)
    success_count = 0
    fail_count = 0
    
    logger.info(f"开始发送广播，总用户数: {total_users}")
    
    # 分批发送
    batch_size = 5  # 减小每批用户数量
    delay = 3  # 增加批次间延迟
    
    for i in range(0, total_users, batch_size):
        batch = users[i:i + batch_size]
        logger.info(f"正在处理第 {i//batch_size + 1} 批，共 {len(batch)} 个用户")
        
        # 创建发送任务
        tasks = [send_message(bot, user_id, BROADCAST_CONTENT) for user_id in batch]
        
        # 等待当前批次完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 更新统计
        batch_success = sum(1 for r in results if r is True)
        batch_fail = sum(1 for r in results if r is not True)
        success_count += batch_success
        fail_count += batch_fail
        
        logger.info(f"批次完成 - 成功: {batch_success}, 失败: {batch_fail}")
        
        # 批次间延迟
        if i + batch_size < total_users:
            logger.info(f"等待 {delay} 秒后发送下一批...")
            await asyncio.sleep(delay)
    
    # 打印最终统计
    logger.info("广播任务完成")
    logger.info(f"""
📊 广播统计:
• 总用户数: {total_users}
• 成功: {success_count}
• 失败: {fail_count}
• 成功率: {(success_count/total_users*100):.1f}%
""")

async def test_broadcast():
    """测试广播给特定用户"""
    logger.info("开始测试广播")
    test_user_id = 7346442935
    
    # 创建bot实例，配置连接池
    request = HTTPXRequest(
        connection_pool_size=5,
        pool_timeout=60.0,
        read_timeout=60.0,
        write_timeout=60.0
    )
    bot = Bot(token=BOT_TOKEN, request=request)
    
    # 发送测试消息
    success = await send_message(bot, test_user_id, BROADCAST_CONTENT)
    
    if success:
        logger.info(f"测试广播成功发送给用户 {test_user_id}")
    else:
        logger.error(f"测试广播发送给用户 {test_user_id} 失败")

if __name__ == "__main__":
    try:
        asyncio.run(broadcast_messages())  # 注释掉原来的广播
        # asyncio.run(test_broadcast())  # 运行测试广播
    except KeyboardInterrupt:
        logger.info("广播任务被用户中断")
    except Exception as e:
        logger.error(f"广播任务发生错误: {str(e)}")