Traceback (most recent call last):
  File "/python/Tree Sg Bot/main.py", line 16, in <module>
    from config import *
  File "/python/Tree Sg Bot/config.py", line 2, in <module>
    from dotenv import load_dotenv
ModuleNotFoundError: No module named 'dotenv'
2025-08-12 17:57:06,673 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 17:57:06,846 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 17:57:06,849 - telegram.ext.Application - INFO - Application started
2025-08-12 17:57:29,171 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:57:29,175 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 115, in network_retry_loop
    if not await do_action():
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 108, in do_action
    return action_cb_task.result()
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 670, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:57:34,521 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:57:34,524 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 115, in network_retry_loop
    if not await do_action():
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 108, in do_action
    return action_cb_task.result()
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 670, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:57:37,855 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:57:38,774 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 17:57:40,869 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:57:40,870 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 115, in network_retry_loop
    if not await do_action():
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 108, in do_action
    return action_cb_task.result()
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 670, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:57:44,255 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:57:44,964 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:57:44,982 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 17:57:45,362 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 17:57:47,474 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 409 Conflict"
2025-08-12 17:57:47,478 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 115, in network_retry_loop
    if not await do_action():
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_utils/networkloop.py", line 108, in do_action
    return action_cb_task.result()
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_updater.py", line 340, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 670, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4611, in get_updates
    await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-12 17:57:56,516 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:57:57,228 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 17:57:59,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:57:59,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 17:58:07,517 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:58:07,519 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 17:58:07,520 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 17:58:10,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 17:58:10,736 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 17:58:10,738 - telegram.ext.Application - INFO - Application started
2025-08-12 17:58:15,704 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:58:16,071 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 17:58:16,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:58:17,278 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 17:58:17,644 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChat "HTTP/1.1 200 OK"
2025-08-12 17:58:18,056 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 17:58:47,118 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:59:17,303 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 17:59:47,487 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:00:02,061 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:00:02,063 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 18:00:02,064 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 18:00:04,578 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 18:00:04,747 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 18:00:04,750 - telegram.ext.Application - INFO - Application started
2025-08-12 18:00:35,274 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:01:05,463 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:01:12,155 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:01:12,854 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:01:13,217 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:01:16,880 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:01:19,674 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:01:20,648 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:01:22,459 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:01:52,638 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:02:22,821 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:02:53,001 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:03:23,185 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:03:53,377 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:04:23,572 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:04:53,764 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:04:54,217 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:04:55,009 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:05:03,826 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:05:04,536 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:05:04,884 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChat "HTTP/1.1 200 OK"
2025-08-12 18:05:05,408 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:05:34,012 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:06:04,201 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:06:34,391 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:07:04,582 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:07:34,763 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:04,941 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:17,523 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:18,406 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:08:20,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:20,370 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:20,747 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChat "HTTP/1.1 200 OK"
2025-08-12 18:08:21,232 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:08:22,273 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:22,726 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:23,097 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:08:24,322 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:24,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:24,866 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChat "HTTP/1.1 200 OK"
2025-08-12 18:08:25,236 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:08:31,653 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:32,378 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:08:33,129 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:33,495 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:33,862 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:08:35,144 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:35,490 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:35,866 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:08:36,564 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:36,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:37,338 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChat "HTTP/1.1 200 OK"
2025-08-12 18:08:37,709 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:08:38,723 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:08:39,080 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:08:39,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:09:08,901 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:09:39,085 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:10:02,284 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:10:02,286 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 18:10:02,287 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 18:10:04,811 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 18:10:04,991 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 18:10:04,994 - telegram.ext.Application - INFO - Application started
2025-08-12 18:10:35,520 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:11:02,116 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:11:32,297 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:12:02,477 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:12:32,661 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:12:43,040 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:12:43,766 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:12:44,131 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:13:13,237 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:13:43,416 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:14:13,592 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:14:43,772 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:15:13,948 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:15:44,140 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:16:14,334 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:16:44,518 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:17:14,700 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:17:44,882 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:18:15,088 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:18:45,273 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:19:15,459 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:19:45,646 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:19:58,890 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:20:02,550 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:20:02,552 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 18:20:02,552 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 18:20:05,092 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 18:20:05,264 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 18:20:05,266 - telegram.ext.Application - INFO - Application started
2025-08-12 18:20:35,789 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:21:05,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:21:36,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:22:06,337 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:22:36,522 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:23:06,709 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:23:36,899 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:24:07,096 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:24:37,280 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:25:07,467 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:25:37,654 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:26:07,842 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:26:38,032 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:27:08,219 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:27:38,404 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:28:08,591 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:28:38,781 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:29:08,963 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:29:39,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:30:01,688 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:30:01,690 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 18:30:01,691 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 18:30:04,311 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 18:30:04,489 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 18:30:04,491 - telegram.ext.Application - INFO - Application started
2025-08-12 18:30:35,019 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:31:05,202 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:31:35,387 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:32:05,579 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:32:35,776 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:33:05,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:33:36,163 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:34:06,349 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:34:36,540 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:35:06,727 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:35:36,914 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:36:07,104 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:36:37,292 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:37:07,522 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:37:37,719 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:38:07,913 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:38:38,097 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:39:08,284 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:39:38,469 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:40:01,892 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:40:01,894 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 18:40:01,894 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 18:40:04,487 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 18:40:04,663 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 18:40:04,666 - telegram.ext.Application - INFO - Application started
2025-08-12 18:40:35,178 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:41:05,360 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:41:35,546 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:42:05,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:42:35,905 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:43:06,092 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:43:36,279 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:44:06,468 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:44:36,648 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:44:49,600 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:44:50,362 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:44:55,189 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:44:55,533 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 18:44:55,945 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 18:45:03,291 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:45:04,135 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:45:33,568 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:46:03,749 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:46:04,037 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:46:04,737 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 18:46:05,080 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 18:46:05,491 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:46:05,494 - __main__ - INFO - 请求URL: http://api.qnm6.top/api/dw?token=0edf486d44ad7f0bba77a49f947f8cd4&phone=13473086808
2025-08-12 18:46:07,333 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteMessage "HTTP/1.1 200 OK"
2025-08-12 18:46:07,776 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:46:08,319 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 18:46:34,224 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:47:04,404 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:47:34,580 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:48:04,757 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:48:34,941 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:49:05,126 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:49:35,303 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:50:02,091 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:50:02,094 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 18:50:02,095 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 18:50:04,739 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 18:50:04,909 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 18:50:04,912 - telegram.ext.Application - INFO - Application started
2025-08-12 18:50:35,439 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:51:05,629 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:51:35,812 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:52:05,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:52:36,187 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:53:06,370 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:53:36,557 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:54:06,751 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:54:36,954 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:55:07,140 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:55:37,324 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:56:07,513 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:56:37,704 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:57:07,891 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:57:38,081 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:58:08,269 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:58:38,456 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:59:08,640 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 18:59:38,827 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:00:02,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:00:02,298 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 19:00:02,299 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 19:00:04,891 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 19:00:05,060 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 19:00:05,064 - telegram.ext.Application - INFO - Application started
2025-08-12 19:00:35,586 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:01:05,768 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:01:35,953 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:02:06,142 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:02:36,327 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:03:06,513 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:03:36,694 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:04:06,876 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:04:37,058 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:05:07,241 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:05:37,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:06:07,620 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:06:37,804 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:07:07,988 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:07:38,173 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:08:08,356 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:08:38,536 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:09:08,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:09:38,897 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:10:02,482 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:10:02,485 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 19:10:02,486 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 19:10:05,066 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 19:10:05,238 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 19:10:05,241 - telegram.ext.Application - INFO - Application started
2025-08-12 19:10:35,767 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:11:05,964 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:11:36,151 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:12:06,337 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:12:36,527 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:13:06,719 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:13:19,431 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:13:20,358 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 19:13:49,356 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:14:19,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:14:49,735 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:15:19,928 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:15:50,122 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:16:20,313 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:16:50,501 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:17:20,691 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:17:44,962 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:17:45,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 19:17:46,058 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 19:18:15,156 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:18:45,341 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:19:15,536 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:19:45,724 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:20:01,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:20:01,684 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 19:20:01,685 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 19:20:04,305 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 19:20:04,478 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 19:20:04,481 - telegram.ext.Application - INFO - Application started
2025-08-12 19:20:35,006 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:21:05,186 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:21:24,935 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:21:55,123 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:22:25,307 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:22:55,489 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:23:25,675 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:23:55,863 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:24:26,164 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:24:56,351 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:25:26,535 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:25:56,713 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:26:13,214 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:26:14,299 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 19:26:24,663 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:26:24,668 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 19:26:54,853 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:27:25,038 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:27:55,224 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:28:25,409 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:28:55,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:29:25,792 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:29:28,488 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:29:58,685 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:30:04,848 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:30:04,849 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 19:30:04,850 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 19:40:04,703 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 19:40:04,878 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 19:40:04,880 - telegram.ext.Application - INFO - Application started
2025-08-12 19:40:35,402 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:41:05,583 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:41:35,764 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:42:05,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:42:36,137 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:43:06,324 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:43:36,510 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:44:06,698 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:44:36,882 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:45:07,067 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:45:37,254 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:46:07,444 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:46:37,628 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:47:07,811 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:47:37,997 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:47:59,856 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:47:59,863 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 19:48:30,052 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:48:47,042 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:49:17,222 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:49:47,400 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:49:50,420 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:49:51,120 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 19:49:51,484 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 19:50:02,134 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:50:02,136 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 19:50:02,136 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 19:50:04,865 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 19:50:05,036 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 19:50:05,038 - telegram.ext.Application - INFO - Application started
2025-08-12 19:50:14,258 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:50:44,439 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:51:14,622 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:51:44,832 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:52:15,022 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:52:45,205 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:53:15,388 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:53:45,588 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:54:15,778 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:54:45,959 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:55:16,140 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:55:46,323 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:56:16,515 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:56:46,694 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:57:16,874 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:57:47,068 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:58:17,247 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:58:47,426 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:59:17,610 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:59:33,118 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:59:37,566 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:59:38,448 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 19:59:51,508 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 19:59:52,205 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 19:59:52,552 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 19:59:52,908 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:00:02,588 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:00:02,591 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 20:00:02,598 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 20:00:05,304 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 20:00:05,475 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 20:00:05,477 - telegram.ext.Application - INFO - Application started
2025-08-12 20:00:35,987 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:00:45,529 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:00:46,057 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 20:00:46,226 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 20:00:46,585 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:00:46,588 - __main__ - INFO - 请求URL: https://api.qnm6.top/api/gh1/?token=0edf486d44ad7f0bba77a49f947f8cd4&xm=江海森&hm=360781200410165559
2025-08-12 20:00:48,721 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteMessage "HTTP/1.1 200 OK"
2025-08-12 20:00:53,736 - __main__ - ERROR - 其他异常: Timed out
2025-08-12 20:00:54,256 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 400 Bad Request"
2025-08-12 20:00:54,258 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_backends/anyio.py", line 37, in read
    return b""
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/python/Tree Sg Bot/main.py", line 1294, in handle_gh_command
    await update.message.reply_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 2304, in reply_photo
    return await self.get_bot().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 3107, in send_photo
    return await super().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 1484, in send_photo
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 290, in do_request
    raise TimedOut from err
telegram.error.TimedOut: Timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 70, in wrapper
    return await func(update, context, *args, **kwargs)
  File "/python/Tree Sg Bot/main.py", line 1365, in handle_gh_command
    await processing_message.edit_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 3904, in edit_text
    return await self.get_bot().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 1723, in edit_message_text
    return await super().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4294, in edit_message_text
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.BadRequest: Message to edit not found
2025-08-12 20:01:03,841 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:01:04,578 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:01:10,674 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:01:11,371 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 20:01:11,416 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:01:11,761 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 20:01:12,103 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 20:01:12,467 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChat "HTTP/1.1 200 OK"
2025-08-12 20:01:12,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 20:01:41,595 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:01:59,682 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:02:00,206 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 20:02:00,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 20:02:00,732 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:02:00,735 - __main__ - INFO - 请求URL: https://api.qnm6.top/api/gh1/?token=0edf486d44ad7f0bba77a49f947f8cd4&xm=张铭俊&hm=441881199609221416
2025-08-12 20:02:02,512 - __main__ - ERROR - 请求异常: Expecting value: line 1 column 1 (char 0)
2025-08-12 20:02:02,863 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 20:02:29,872 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:02:30,622 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:02:31,147 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 20:02:31,321 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 20:02:31,683 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:02:31,685 - __main__ - INFO - 请求URL: https://api.qnm6.top/api/gh1/?token=0edf486d44ad7f0bba77a49f947f8cd4&xm=岑雄&hm=45262620041222259X
2025-08-12 20:02:34,030 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteMessage "HTTP/1.1 200 OK"
2025-08-12 20:02:39,039 - __main__ - ERROR - 其他异常: Timed out
2025-08-12 20:02:39,553 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 400 Bad Request"
2025-08-12 20:02:39,556 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_backends/anyio.py", line 37, in read
    return b""
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/python/Tree Sg Bot/main.py", line 1294, in handle_gh_command
    await update.message.reply_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 2304, in reply_photo
    return await self.get_bot().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 3107, in send_photo
    return await super().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 1484, in send_photo
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 290, in do_request
    raise TimedOut from err
telegram.error.TimedOut: Timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 70, in wrapper
    return await func(update, context, *args, **kwargs)
  File "/python/Tree Sg Bot/main.py", line 1365, in handle_gh_command
    await processing_message.edit_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 3904, in edit_text
    return await self.get_bot().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 1723, in edit_message_text
    return await super().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4294, in edit_message_text
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.BadRequest: Message to edit not found
2025-08-12 20:03:00,803 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:03:30,985 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:04:01,169 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:04:31,353 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:05:01,549 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:05:31,733 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:06:01,923 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:06:32,107 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:07:02,282 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:07:05,840 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:07:06,534 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 20:07:06,900 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 20:07:36,028 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:08:06,212 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:08:36,392 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:09:06,569 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:09:36,754 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:10:01,881 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:10:01,884 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 20:10:01,885 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 20:10:04,672 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 20:10:04,843 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 20:10:04,846 - telegram.ext.Application - INFO - Application started
2025-08-12 20:10:35,357 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:11:05,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:11:35,734 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:12:05,916 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:12:36,093 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:13:06,270 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:13:36,458 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:13:48,898 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:13:48,917 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 20:14:19,108 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:14:49,288 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:15:19,464 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:15:49,643 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:16:19,827 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:16:50,017 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:17:20,199 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:17:50,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:18:20,558 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:18:50,746 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:19:20,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:19:51,109 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:20:02,094 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:20:02,097 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 20:20:02,097 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 20:20:04,769 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 20:20:04,941 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 20:20:04,945 - telegram.ext.Application - INFO - Application started
2025-08-12 20:20:35,468 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:21:05,661 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:21:35,852 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:22:02,747 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:22:03,617 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:22:33,044 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:23:03,223 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:23:29,526 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:23:30,530 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:23:59,912 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:24:30,098 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:25:00,287 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:25:01,469 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:25:02,163 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 20:25:02,530 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 20:25:05,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:25:22,342 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:25:23,361 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:25:47,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:26:17,552 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:26:47,736 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:27:09,656 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:27:10,358 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 20:27:10,787 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:27:19,446 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:27:44,523 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:27:45,044 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 20:27:45,387 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 20:27:45,807 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:28:14,712 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:28:25,135 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:28:25,664 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 20:28:25,834 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 20:28:26,279 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:28:26,282 - __main__ - INFO - 请求URL: https://api.qnm6.top/api/gh1/?token=0edf486d44ad7f0bba77a49f947f8cd4&xm=陆志红&hm=450881199301086108
2025-08-12 20:28:28,003 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteMessage "HTTP/1.1 200 OK"
2025-08-12 20:28:33,026 - __main__ - ERROR - 其他异常: Timed out
2025-08-12 20:28:33,563 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 400 Bad Request"
2025-08-12 20:28:33,565 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_backends/anyio.py", line 37, in read
    return b""
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/python/Tree Sg Bot/main.py", line 1294, in handle_gh_command
    await update.message.reply_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 2304, in reply_photo
    return await self.get_bot().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 3107, in send_photo
    return await super().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 1484, in send_photo
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 290, in do_request
    raise TimedOut from err
telegram.error.TimedOut: Timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 70, in wrapper
    return await func(update, context, *args, **kwargs)
  File "/python/Tree Sg Bot/main.py", line 1365, in handle_gh_command
    await processing_message.edit_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 3904, in edit_text
    return await self.get_bot().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 1723, in edit_message_text
    return await super().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4294, in edit_message_text
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.BadRequest: Message to edit not found
2025-08-12 20:28:43,560 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:29:13,759 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:29:43,954 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:30:01,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:30:01,973 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 20:30:01,973 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 20:30:04,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 20:30:04,719 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 20:30:04,722 - telegram.ext.Application - INFO - Application started
2025-08-12 20:30:35,244 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:31:05,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:31:17,609 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:31:47,788 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:31:52,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:32:22,214 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:32:52,405 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:33:22,591 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:33:52,775 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:34:22,964 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:34:53,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:35:23,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:35:53,537 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:36:23,728 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:36:50,196 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:36:50,739 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 20:36:50,930 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 20:36:51,331 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:36:51,333 - __main__ - INFO - 请求URL: https://api.qnm6.top/api/gh1/?token=0edf486d44ad7f0bba77a49f947f8cd4&xm=梁存林&hm=450924198707255147
2025-08-12 20:36:54,502 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteMessage "HTTP/1.1 200 OK"
2025-08-12 20:36:59,516 - __main__ - ERROR - 其他异常: Timed out
2025-08-12 20:37:00,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 400 Bad Request"
2025-08-12 20:37:00,027 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_backends/anyio.py", line 37, in read
    return b""
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/python/Tree Sg Bot/main.py", line 1294, in handle_gh_command
    await update.message.reply_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 2304, in reply_photo
    return await self.get_bot().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 3107, in send_photo
    return await super().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 1484, in send_photo
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 290, in do_request
    raise TimedOut from err
telegram.error.TimedOut: Timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 70, in wrapper
    return await func(update, context, *args, **kwargs)
  File "/python/Tree Sg Bot/main.py", line 1365, in handle_gh_command
    await processing_message.edit_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 3904, in edit_text
    return await self.get_bot().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 1723, in edit_message_text
    return await super().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4294, in edit_message_text
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.BadRequest: Message to edit not found
2025-08-12 20:37:20,391 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:37:50,575 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:38:20,767 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:38:50,963 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:39:21,147 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:39:51,338 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:40:02,141 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:40:02,143 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 20:40:02,143 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 20:40:04,678 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 20:40:04,851 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 20:40:04,853 - telegram.ext.Application - INFO - Application started
2025-08-12 20:40:35,377 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:41:05,569 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:41:35,777 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:42:05,968 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:42:36,152 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:43:06,341 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:43:36,537 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:44:06,727 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:44:36,911 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:45:07,106 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:45:37,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:46:07,477 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:46:37,664 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:47:07,857 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:47:38,048 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:48:08,237 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:48:38,432 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:48:59,655 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:49:00,652 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:49:03,815 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:49:10,530 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:49:17,553 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:49:18,286 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:49:21,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:49:52,112 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:50:02,358 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:50:02,360 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 20:50:02,360 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 20:50:05,246 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 20:50:05,419 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 20:50:05,421 - telegram.ext.Application - INFO - Application started
2025-08-12 20:50:35,949 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:51:06,133 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:51:36,320 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:52:06,511 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:52:36,701 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:53:06,893 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:53:37,088 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:54:07,276 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:54:37,462 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:55:07,649 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:55:37,836 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:56:08,029 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:56:38,224 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:57:08,419 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:57:38,607 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:58:08,788 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:58:31,768 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:58:32,650 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:58:34,606 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:58:35,006 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 20:58:35,362 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 20:58:36,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:58:37,294 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 20:59:07,121 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 20:59:37,307 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:00:02,911 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:00:02,913 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 21:00:02,914 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 21:00:05,499 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 21:00:05,676 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 21:00:05,680 - telegram.ext.Application - INFO - Application started
2025-08-12 21:00:36,189 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:01:06,403 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:01:36,587 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:02:06,761 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:02:36,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:03:07,119 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:03:37,302 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:04:07,488 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:04:37,678 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:05:07,864 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:05:38,043 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:06:08,219 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:06:38,396 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:07:08,578 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:07:38,764 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:08:08,946 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:08:39,126 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:09:09,302 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:09:39,484 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:10:02,144 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:10:02,145 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 21:10:02,146 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 21:10:04,741 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 21:10:04,913 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 21:10:04,915 - telegram.ext.Application - INFO - Application started
2025-08-12 21:10:35,432 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:11:05,615 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:11:35,805 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:11:38,640 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:11:39,716 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 21:12:09,054 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:12:39,237 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:13:09,417 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:13:39,604 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:14:09,791 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:14:39,979 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:15:10,167 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:15:40,351 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:16:10,559 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:16:40,740 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:17:10,921 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:17:41,106 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:18:11,287 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:18:41,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:19:11,668 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:19:41,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:20:02,374 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:20:02,377 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 21:20:02,377 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 21:20:05,001 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 21:20:05,169 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 21:20:05,171 - telegram.ext.Application - INFO - Application started
2025-08-12 21:20:35,692 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:21:05,874 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:21:36,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:22:06,238 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:22:36,430 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:23:06,617 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:23:36,812 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:24:06,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:24:37,186 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:25:07,374 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:25:37,605 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:26:07,794 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:26:37,980 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:27:08,171 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:27:38,356 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:28:08,539 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:28:38,721 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:29:08,917 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:29:14,491 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:29:15,226 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 21:29:17,620 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:29:17,964 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 21:29:18,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 21:29:42,481 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:29:43,464 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 21:30:02,528 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:30:02,530 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 21:30:02,530 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 21:30:05,118 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 21:30:05,287 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 21:30:05,290 - telegram.ext.Application - INFO - Application started
2025-08-12 21:30:35,814 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:31:06,001 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:31:36,183 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:32:06,362 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:32:36,545 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:33:06,729 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:33:36,907 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:34:07,090 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:34:37,284 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:35:07,471 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:35:37,658 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:36:07,848 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:36:38,029 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:37:08,208 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:37:38,394 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:38:08,583 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:38:38,772 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:39:08,962 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:39:39,147 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:40:02,605 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:40:02,607 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 21:40:02,607 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 21:40:05,285 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 21:40:05,459 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 21:40:05,461 - telegram.ext.Application - INFO - Application started
2025-08-12 21:40:35,974 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:41:06,158 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:41:36,338 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:42:06,516 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:42:36,699 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:43:06,882 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:43:37,069 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:44:07,258 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:44:37,450 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:45:07,629 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:45:37,810 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:46:07,997 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:46:38,178 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:47:08,360 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:47:38,552 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:48:08,742 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:48:38,930 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:49:09,114 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:49:39,294 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:50:01,782 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:50:01,785 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 21:50:01,786 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 21:50:04,597 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 21:50:04,772 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 21:50:04,774 - telegram.ext.Application - INFO - Application started
2025-08-12 21:50:35,300 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:51:05,489 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:51:35,683 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:52:05,869 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:52:36,054 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:53:06,236 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:53:36,434 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:54:06,632 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:54:36,825 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:55:07,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:55:37,232 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:56:07,432 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:56:37,621 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:57:07,813 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:57:37,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:58:08,185 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:58:38,375 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:59:08,566 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 21:59:38,783 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:00:01,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:00:01,971 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 22:00:01,979 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 22:00:04,620 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 22:00:04,789 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 22:00:04,792 - telegram.ext.Application - INFO - Application started
2025-08-12 22:00:17,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:00:17,938 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 22:00:48,132 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:01:18,323 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:01:48,524 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:02:18,721 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:02:48,914 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:03:19,104 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:03:49,288 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:04:19,472 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:04:49,661 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:05:19,852 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:05:50,044 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:06:20,233 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:06:50,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:07:20,615 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:07:50,805 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:08:20,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:08:51,192 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:09:21,058 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:09:21,768 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 22:09:22,129 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 22:09:24,647 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:09:25,283 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:09:54,994 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:10:02,152 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:10:02,155 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 22:10:02,156 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 22:10:04,684 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 22:10:04,853 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 22:10:04,855 - telegram.ext.Application - INFO - Application started
2025-08-12 22:10:35,367 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:10:35,543 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:10:35,552 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 22:11:05,735 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:11:35,910 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:12:06,095 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:12:07,298 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:12:21,223 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:12:21,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:12:22,270 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:12:22,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:12:22,649 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:12:22,818 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:12:22,986 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:12:23,372 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:12:25,430 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:12:26,460 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:12:27,100 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:12:57,278 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:13:18,320 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:13:19,029 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:13:19,370 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:13:19,777 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:13:19,779 - __main__ - INFO - 请求URL: https://api.qnm6.top/api/gh1/?token=0edf486d44ad7f0bba77a49f947f8cd4&xm=梁存林&hm=450924198707255147
2025-08-12 22:13:21,558 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteMessage "HTTP/1.1 200 OK"
2025-08-12 22:13:21,907 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendPhoto "HTTP/1.1 400 Bad Request"
2025-08-12 22:13:21,909 - __main__ - ERROR - 其他异常: Wrong type of the web page content
2025-08-12 22:13:22,078 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 400 Bad Request"
2025-08-12 22:13:22,079 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/python/Tree Sg Bot/main.py", line 1294, in handle_gh_command
    await update.message.reply_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 2304, in reply_photo
    return await self.get_bot().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 3107, in send_photo
    return await super().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 1484, in send_photo
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.BadRequest: Wrong type of the web page content

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 70, in wrapper
    return await func(update, context, *args, **kwargs)
  File "/python/Tree Sg Bot/main.py", line 1365, in handle_gh_command
    await processing_message.edit_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 3904, in edit_text
    return await self.get_bot().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 1723, in edit_message_text
    return await super().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4294, in edit_message_text
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.BadRequest: Message to edit not found
2025-08-12 22:13:48,509 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:14:18,688 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:14:48,870 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:15:00,284 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:15:30,466 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:15:46,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:15:47,836 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:16:17,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:16:47,339 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:17:17,525 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:17:47,715 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:18:17,892 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:18:48,072 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:19:18,254 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:19:33,666 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:19:34,538 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:20:02,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:20:02,447 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 22:20:02,447 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 22:20:05,034 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 22:20:05,206 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 22:20:05,208 - telegram.ext.Application - INFO - Application started
2025-08-12 22:20:25,250 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:20:25,258 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 22:20:44,614 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:20:51,627 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:20:52,553 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:21:21,945 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:21:52,133 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:22:22,315 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:22:52,507 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:23:08,634 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:23:38,821 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:24:09,005 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:24:39,192 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:25:09,383 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:25:39,578 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:26:09,760 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:26:39,945 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:27:10,139 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:27:40,331 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:28:10,519 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:28:26,340 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:28:27,317 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:28:31,706 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:28:58,024 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:29:01,084 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:29:01,788 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 22:29:02,221 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 22:29:06,073 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:29:06,076 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 22:29:09,267 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:29:14,253 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:29:44,442 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:30:02,223 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:30:02,226 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 22:30:02,226 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 22:30:04,953 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 22:30:05,121 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 22:30:05,125 - telegram.ext.Application - INFO - Application started
2025-08-12 22:30:19,292 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:30:20,160 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:30:22,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:30:22,496 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 22:30:22,667 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 22:30:23,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 22:30:29,286 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:30:57,924 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:30:58,885 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:31:01,117 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:31:02,210 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:31:02,583 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:31:05,442 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:31:05,806 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 22:31:06,205 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 22:31:13,207 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:31:13,388 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:31:14,079 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:31:14,468 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:31:16,731 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:31:17,255 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:31:21,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:31:21,753 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:31:24,625 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:31:25,038 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 22:31:25,392 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 22:31:26,567 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:31:26,906 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 22:31:27,278 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 22:31:34,555 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:31:39,605 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:31:50,187 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:31:50,901 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:31:53,777 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:32:02,923 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:32:03,446 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:32:03,618 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:32:04,000 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:32:33,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:32:39,555 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:32:40,090 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:32:40,264 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:32:40,627 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:32:40,629 - __main__ - INFO - 请求URL: https://api.qnm6.top/api/gh2/?token=0edf486d44ad7f0bba77a49f947f8cd4&xm=陈平&hm=420583197606160016
2025-08-12 22:32:42,185 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteMessage "HTTP/1.1 200 OK"
2025-08-12 22:32:47,198 - __main__ - ERROR - 其他异常: Timed out
2025-08-12 22:32:47,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 400 Bad Request"
2025-08-12 22:32:47,727 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_backends/anyio.py", line 37, in read
    return b""
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/python/Tree Sg Bot/main.py", line 1294, in handle_gh_command
    await update.message.reply_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 2304, in reply_photo
    return await self.get_bot().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 3107, in send_photo
    return await super().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 1484, in send_photo
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 290, in do_request
    raise TimedOut from err
telegram.error.TimedOut: Timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 70, in wrapper
    return await func(update, context, *args, **kwargs)
  File "/python/Tree Sg Bot/main.py", line 1365, in handle_gh_command
    await processing_message.edit_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 3904, in edit_text
    return await self.get_bot().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 1723, in edit_message_text
    return await super().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4294, in edit_message_text
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.BadRequest: Message to edit not found
2025-08-12 22:33:09,744 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:33:39,928 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:34:10,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:34:40,290 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:35:10,468 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:35:40,649 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:36:10,833 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:36:41,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:37:11,216 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:37:41,409 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:38:04,569 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:38:04,577 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 22:38:34,758 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:39:04,995 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:39:35,178 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:40:02,467 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:40:02,469 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 22:40:02,469 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 22:40:05,009 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 22:40:05,176 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 22:40:05,179 - telegram.ext.Application - INFO - Application started
2025-08-12 22:40:35,572 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:40:49,481 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:41:19,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:41:49,848 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:42:20,033 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:42:50,218 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:43:20,397 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:43:50,580 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:43:55,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:43:56,437 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:44:25,849 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:44:56,035 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:45:26,221 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:45:56,404 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:46:26,589 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:46:40,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:46:40,611 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 22:47:10,804 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:47:40,990 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:48:11,170 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:48:41,352 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:49:11,543 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:49:41,731 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:50:01,787 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:50:01,789 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 22:50:01,790 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 22:50:04,342 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 22:50:04,514 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 22:50:04,517 - telegram.ext.Application - INFO - Application started
2025-08-12 22:50:35,029 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:51:05,206 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:51:35,380 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:52:05,558 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:52:11,360 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:52:12,198 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:52:21,664 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:52:22,399 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:52:22,738 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:52:23,245 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:52:23,247 - __main__ - INFO - 请求URL: https://api.qnm6.top/api/gh1/?token=0edf486d44ad7f0bba77a49f947f8cd4&xm=彭慧滢&hm=430121200011237040
2025-08-12 22:52:38,399 - __main__ - ERROR - 请求异常: Expecting value: line 1 column 1 (char 0)
2025-08-12 22:52:39,153 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 22:52:51,839 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:52:54,722 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:52:55,251 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:52:55,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:52:55,845 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:52:55,847 - __main__ - INFO - 请求URL: https://api.qnm6.top/api/gh2/?token=0edf486d44ad7f0bba77a49f947f8cd4&xm=彭慧滢&hm=430121200011237040
2025-08-12 22:52:59,361 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteMessage "HTTP/1.1 200 OK"
2025-08-12 22:53:04,376 - __main__ - ERROR - 其他异常: Timed out
2025-08-12 22:53:04,885 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 400 Bad Request"
2025-08-12 22:53:04,886 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_backends/anyio.py", line 37, in read
    return b""
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/python/Tree Sg Bot/main.py", line 1294, in handle_gh_command
    await update.message.reply_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 2304, in reply_photo
    return await self.get_bot().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 3107, in send_photo
    return await super().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 1484, in send_photo
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 290, in do_request
    raise TimedOut from err
telegram.error.TimedOut: Timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 70, in wrapper
    return await func(update, context, *args, **kwargs)
  File "/python/Tree Sg Bot/main.py", line 1365, in handle_gh_command
    await processing_message.edit_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 3904, in edit_text
    return await self.get_bot().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 1723, in edit_message_text
    return await super().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4294, in edit_message_text
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.BadRequest: Message to edit not found
2025-08-12 22:53:24,899 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:53:25,757 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:53:26,267 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:53:26,439 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 22:53:26,893 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:53:26,896 - __main__ - INFO - 请求URL: https://api.qnm6.top/api/gh1/?token=0edf486d44ad7f0bba77a49f947f8cd4&xm=彭慧滢&hm=430121200011237040
2025-08-12 22:53:28,125 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteMessage "HTTP/1.1 200 OK"
2025-08-12 22:53:33,136 - __main__ - ERROR - 其他异常: Timed out
2025-08-12 22:53:33,645 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 400 Bad Request"
2025-08-12 22:53:33,647 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_backends/anyio.py", line 37, in read
    return b""
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/python/Tree Sg Bot/main.py", line 1294, in handle_gh_command
    await update.message.reply_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 2304, in reply_photo
    return await self.get_bot().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 3107, in send_photo
    return await super().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 1484, in send_photo
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 290, in do_request
    raise TimedOut from err
telegram.error.TimedOut: Timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 70, in wrapper
    return await func(update, context, *args, **kwargs)
  File "/python/Tree Sg Bot/main.py", line 1365, in handle_gh_command
    await processing_message.edit_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 3904, in edit_text
    return await self.get_bot().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 1723, in edit_message_text
    return await super().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4294, in edit_message_text
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.BadRequest: Message to edit not found
2025-08-12 22:53:47,156 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:53:47,162 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 22:54:17,342 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:54:18,147 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:54:18,151 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 22:54:30,416 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:54:30,423 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 22:54:39,064 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:54:39,067 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 22:54:52,152 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:54:52,157 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 22:55:10,367 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:55:10,371 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 22:55:40,544 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:56:10,720 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:56:40,895 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:57:11,084 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:57:41,274 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:58:11,460 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:58:41,647 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:59:02,648 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:59:03,642 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 22:59:32,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:59:36,923 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 22:59:37,731 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:00:01,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:00:01,982 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 23:00:01,983 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 23:00:04,499 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 23:00:04,672 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 23:00:04,674 - telegram.ext.Application - INFO - Application started
2025-08-12 23:00:14,410 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:00:15,166 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 23:00:15,630 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:00:44,600 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:01:14,785 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:01:44,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:01:57,473 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:01:58,156 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 23:01:58,648 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:02:06,510 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:02:07,309 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:02:18,797 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:02:18,803 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 23:02:39,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:02:40,502 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:02:40,860 - __main__ - ERROR - 请求异常: Expecting value: line 1 column 1 (char 0)
2025-08-12 23:02:41,262 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:02:50,673 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:02:51,367 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 23:02:51,748 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:03:11,987 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:03:12,803 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 23:03:13,251 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 23:03:13,684 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:03:13,687 - __main__ - INFO - 请求URL: https://api.qnm6.top/api/gh1/?token=0edf486d44ad7f0bba77a49f947f8cd4&xm=赵志诚&hm=321322201210270131
2025-08-12 23:03:18,195 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteMessage "HTTP/1.1 200 OK"
2025-08-12 23:03:23,208 - __main__ - ERROR - 其他异常: Timed out
2025-08-12 23:03:23,713 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 400 Bad Request"
2025-08-12 23:03:23,714 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_backends/anyio.py", line 37, in read
    return b""
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/python/Tree Sg Bot/main.py", line 1294, in handle_gh_command
    await update.message.reply_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 2304, in reply_photo
    return await self.get_bot().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 3107, in send_photo
    return await super().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 1484, in send_photo
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 290, in do_request
    raise TimedOut from err
telegram.error.TimedOut: Timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 70, in wrapper
    return await func(update, context, *args, **kwargs)
  File "/python/Tree Sg Bot/main.py", line 1365, in handle_gh_command
    await processing_message.edit_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 3904, in edit_text
    return await self.get_bot().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 1723, in edit_message_text
    return await super().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4294, in edit_message_text
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.BadRequest: Message to edit not found
2025-08-12 23:03:42,183 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:04:12,373 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:04:42,559 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:05:12,132 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:05:12,649 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 23:05:12,819 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 23:05:13,241 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:05:13,244 - __main__ - INFO - 请求URL: https://api.qnm6.top/api/gh1/?token=0edf486d44ad7f0bba77a49f947f8cd4&xm=赵兴明&hm=321322198208025992
2025-08-12 23:05:14,585 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteMessage "HTTP/1.1 200 OK"
2025-08-12 23:05:19,596 - __main__ - ERROR - 其他异常: Timed out
2025-08-12 23:05:20,115 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 400 Bad Request"
2025-08-12 23:05:20,117 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/connection.py", line 103, in handle_async_request
    return await self._connection.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 136, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 106, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 177, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_async/http11.py", line 217, in _receive_event
    data = await self._network_stream.read(
  File "/usr/local/lib/python3.9/site-packages/httpcore/_backends/anyio.py", line 37, in read
    return b""
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
  File "/usr/lib64/python3.9/contextlib.py", line 137, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/usr/local/lib/python3.9/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadTimeout

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/python/Tree Sg Bot/main.py", line 1294, in handle_gh_command
    await update.message.reply_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 2304, in reply_photo
    return await self.get_bot().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 3107, in send_photo
    return await super().send_photo(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 1484, in send_photo
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_httpxrequest.py", line 290, in do_request
    raise TimedOut from err
telegram.error.TimedOut: Timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 70, in wrapper
    return await func(update, context, *args, **kwargs)
  File "/python/Tree Sg Bot/main.py", line 1365, in handle_gh_command
    await processing_message.edit_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_message.py", line 3904, in edit_text
    return await self.get_bot().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 1723, in edit_message_text
    return await super().edit_message_text(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 4294, in edit_message_text
    return await self._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 630, in _send_message
    result = await super()._send_message(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 810, in _send_message
    result = await self._post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 698, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_extbot.py", line 370, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.9/site-packages/telegram/_bot.py", line 727, in _do_post
    result = await request.post(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 197, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.9/site-packages/telegram/request/_baserequest.py", line 374, in _request_wrapper
    raise exception
telegram.error.BadRequest: Message to edit not found
2025-08-12 23:05:42,322 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:06:12,505 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:06:42,692 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:07:12,878 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:07:43,071 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:08:13,258 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:08:43,446 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:09:13,631 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:09:43,833 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:10:02,194 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:10:02,197 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 23:10:02,197 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 23:10:04,876 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 23:10:05,051 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 23:10:05,054 - telegram.ext.Application - INFO - Application started
2025-08-12 23:10:19,481 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:10:19,484 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 23:10:49,670 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:11:19,856 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:11:50,041 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:12:20,230 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:12:50,417 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:13:20,606 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:13:50,794 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:14:20,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:14:51,171 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:15:21,357 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:15:51,538 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:16:21,728 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:16:51,917 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:17:22,100 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:17:52,288 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:18:22,484 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:18:43,878 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:18:43,889 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 23:19:08,135 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:19:09,236 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:19:31,158 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:20:01,346 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:20:05,320 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:20:05,322 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 23:20:05,322 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 23:30:05,192 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 23:30:05,366 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 23:30:05,369 - telegram.ext.Application - INFO - Application started
2025-08-12 23:30:28,120 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:30:29,069 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:30:39,314 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:30:40,084 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 23:30:40,521 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:30:46,291 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:30:46,973 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 23:30:47,354 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:31:16,483 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:31:46,677 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:32:16,862 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:32:47,049 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:33:17,237 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:33:47,421 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:34:17,608 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:34:47,797 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:35:17,982 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:35:41,904 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:35:41,908 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 23:36:12,092 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:36:42,283 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:37:12,473 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:37:42,659 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:38:12,847 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:38:43,037 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:39:13,220 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:39:43,408 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:40:02,582 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:40:02,584 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 23:40:02,584 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 23:40:05,462 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 23:40:05,633 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 23:40:05,636 - telegram.ext.Application - INFO - Application started
2025-08-12 23:40:21,207 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:40:51,385 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:41:21,562 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:41:32,765 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:41:33,439 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 23:41:33,798 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:41:37,248 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:41:37,587 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 23:41:37,949 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:42:07,429 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:42:37,613 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:43:07,793 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:43:20,212 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:43:20,901 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 23:43:21,258 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChat "HTTP/1.1 200 OK"
2025-08-12 23:43:21,761 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:43:36,204 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:43:36,902 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 23:43:37,441 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:43:41,037 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:43:45,349 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:43:50,219 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:43:50,743 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 23:43:50,916 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 23:43:51,286 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:43:59,711 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:44:00,438 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 23:44:00,813 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:44:06,032 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:44:06,036 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 23:44:36,223 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:44:40,465 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:44:41,245 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:44:45,386 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:45:15,566 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:45:45,744 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:46:15,934 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:46:46,121 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:47:16,304 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:47:46,489 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:48:16,667 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:48:46,843 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:49:05,036 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:49:05,937 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:49:10,227 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:49:10,574 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 23:49:10,930 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:49:13,355 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:49:13,698 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 23:49:14,076 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:49:22,772 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:49:52,955 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:50:01,758 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:50:01,759 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-12 23:50:01,760 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-12 23:50:04,335 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getMe "HTTP/1.1 200 OK"
2025-08-12 23:50:04,507 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/deleteWebhook "HTTP/1.1 200 OK"
2025-08-12 23:50:04,510 - telegram.ext.Application - INFO - Application started
2025-08-12 23:50:35,014 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:51:05,205 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:51:35,379 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:51:48,687 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:51:49,375 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 23:51:49,716 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 23:51:50,200 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:52:18,869 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:52:49,050 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:53:19,229 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:53:41,211 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:53:41,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 23:53:42,280 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 23:53:42,773 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:55:35,032 - __main__ - ERROR - 请求异常: ('Connection broken: IncompleteRead(392161 bytes read, 1646624 more expected)', IncompleteRead(392161 bytes read, 1646624 more expected))
2025-08-12 23:55:35,047 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:55:35,751 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:56:01,130 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:56:01,135 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/usr/local/lib/python3.9/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
  File "/python/Tree Sg Bot/main.py", line 2535, in check_banned_words
    if update.message.forward_from or update.message.forward_from_chat:
AttributeError: 'Message' object has no attribute 'forward_from'
2025-08-12 23:56:24,779 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:56:25,615 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:56:32,792 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:56:33,532 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 23:56:33,913 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:57:02,985 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:57:33,171 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:58:00,639 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:58:01,167 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 23:58:01,336 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getChatMember "HTTP/1.1 200 OK"
2025-08-12 23:58:01,918 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/sendMessage "HTTP/1.1 200 OK"
2025-08-12 23:58:12,648 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:58:13,325 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/answerCallbackQuery "HTTP/1.1 200 OK"
2025-08-12 23:58:13,679 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/editMessageText "HTTP/1.1 200 OK"
2025-08-12 23:58:42,825 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:59:13,005 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-12 23:59:43,187 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-13 00:00:02,145 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI/getUpdates "HTTP/1.1 200 OK"
2025-08-13 00:00:02,148 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-13 00:00:02,148 - telegram.ext.Application - INFO - Application.stop() complete
