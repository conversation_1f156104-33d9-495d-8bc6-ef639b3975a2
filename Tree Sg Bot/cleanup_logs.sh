#!/bin/bash

# 日志清理脚本
# 用于定时清理 Tree Sg Bot 目录下的日志文件

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="$SCRIPT_DIR"
LOGS_SUBDIR="$SCRIPT_DIR/logs"

# 日志文件保留天数（默认保留7天）
KEEP_DAYS=${1:-7}

# 创建清理日志
CLEANUP_LOG="$SCRIPT_DIR/cleanup.log"

# 记录清理开始时间
echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始清理日志文件，保留最近 $KEEP_DAYS 天的文件" >> "$CLEANUP_LOG"

# 清理函数
cleanup_files() {
    local pattern=$1
    local description=$2
    local count=0
    
    echo "正在清理 $description..."
    
    # 查找并删除超过指定天数的文件
    while IFS= read -r -d '' file; do
        if [[ -f "$file" ]]; then
            echo "删除文件: $file" >> "$CLEANUP_LOG"
            rm -f "$file"
            ((count++))
        fi
    done < <(find "$LOG_DIR" -maxdepth 1 -name "$pattern" -type f -mtime +$KEEP_DAYS -print0 2>/dev/null)
    
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $description: 删除了 $count 个文件" >> "$CLEANUP_LOG"
    echo "$description: 删除了 $count 个文件"
}

# 清理 logs 子目录中的文件
cleanup_logs_subdir() {
    local count=0
    
    if [[ -d "$LOGS_SUBDIR" ]]; then
        echo "正在清理 logs 子目录..."
        
        while IFS= read -r -d '' file; do
            if [[ -f "$file" ]]; then
                echo "删除文件: $file" >> "$CLEANUP_LOG"
                rm -f "$file"
                ((count++))
            fi
        done < <(find "$LOGS_SUBDIR" -name "*.log" -type f -mtime +$KEEP_DAYS -print0 2>/dev/null)
        
        echo "$(date '+%Y-%m-%d %H:%M:%S') - logs子目录: 删除了 $count 个文件" >> "$CLEANUP_LOG"
        echo "logs子目录: 删除了 $count 个文件"
    fi
}

# 显示磁盘使用情况（清理前）
echo "清理前磁盘使用情况:"
du -sh "$LOG_DIR" 2>/dev/null || echo "无法获取磁盘使用情况"

# 执行清理
cleanup_files "broadcast_*.log" "广播日志文件"
cleanup_files "forward_channel_*.log" "转发频道日志文件"
cleanup_logs_subdir

# 显示磁盘使用情况（清理后）
echo "清理后磁盘使用情况:"
du -sh "$LOG_DIR" 2>/dev/null || echo "无法获取磁盘使用情况"

# 记录清理完成时间
echo "$(date '+%Y-%m-%d %H:%M:%S') - 日志清理完成" >> "$CLEANUP_LOG"
echo "日志清理完成！"

# 清理自身的清理日志（保留最近30天）
find "$SCRIPT_DIR" -name "cleanup.log" -type f -mtime +30 -delete 2>/dev/null

exit 0
