#!/bin/bash

# 设置定时任务脚本
# 用于配置日志清理的cron任务

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLEANUP_SCRIPT="$SCRIPT_DIR/cleanup_logs.sh"

echo "正在设置定时清理任务..."

# 检查cleanup_logs.sh是否存在
if [[ ! -f "$CLEANUP_SCRIPT" ]]; then
    echo "错误: 找不到 cleanup_logs.sh 脚本"
    exit 1
fi

# 确保脚本有执行权限
chmod +x "$CLEANUP_SCRIPT"

# 备份当前的crontab
echo "备份当前crontab..."
crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || echo "当前没有crontab任务"

# 检查是否已经存在相同的任务
if crontab -l 2>/dev/null | grep -q "$CLEANUP_SCRIPT"; then
    echo "警告: 已存在相同的清理任务，将替换现有任务"
    # 移除现有的任务
    crontab -l 2>/dev/null | grep -v "$CLEANUP_SCRIPT" | crontab -
fi

# 添加新的定时任务
echo "添加定时清理任务..."

# 创建临时crontab文件
TEMP_CRON=$(mktemp)

# 获取现有的crontab内容
crontab -l 2>/dev/null > "$TEMP_CRON" || true

# 添加新的任务（每天凌晨2点执行，保留7天的日志）
echo "0 2 * * * $CLEANUP_SCRIPT 7 >> $SCRIPT_DIR/cron.log 2>&1" >> "$TEMP_CRON"

# 安装新的crontab
crontab "$TEMP_CRON"

# 清理临时文件
rm -f "$TEMP_CRON"

echo "定时任务设置完成！"
echo "任务详情:"
echo "- 执行时间: 每天凌晨2:00"
echo "- 保留天数: 7天"
echo "- 日志文件: $SCRIPT_DIR/cron.log"
echo ""
echo "当前crontab任务:"
crontab -l

echo ""
echo "如需修改保留天数，请编辑crontab:"
echo "crontab -e"
echo ""
echo "如需立即测试清理脚本，请运行:"
echo "$CLEANUP_SCRIPT [保留天数]"
